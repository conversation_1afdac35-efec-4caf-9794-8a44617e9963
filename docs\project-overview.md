# E-Senpai Project Overview

## Platform Purpose

E-Senpai is a **gaming and entertainment companionship marketplace** that connects customers with senpai (service providers) for personalized gaming experiences, streaming sessions, and entertainment content. The platform enables customers to book flexible-duration services ranging from gaming coaching and companionship to custom content creation, all facilitated through a secure dual-currency system (soda for transactions, caps for rewards) with escrow protection and comprehensive dispute resolution.

## Core Features List

### Gaming & Entertainment Services

- **Activity-based service catalog** where senpai create services under predefined gaming/entertainment activities with hierarchical organization
- **Flexible pricing and duration** allowing senpai to set custom time blocks, hourly rates, and service terms
- **Multi-layered service customization** through pre-defined fields, dynamic form builders, and activity-inherited templates
- **Comprehensive service modifiers** supporting entertainment enhancements, personalized content creation, and open-ended senpai-defined customizations
- **Pre-purchase Q&A system** with cap-based rewards for senpai answers and cap costs for customer questions

### Dual Currency & Transaction System

- **Soda currency** as the primary transaction medium for service payments
- **Cap currency** as a reward/loyalty system earned through platform activity (reviews, order completions, engagement)
- **Escrow-based order management** with automatic release after dispute windows or administrative approval
- **Commission-based revenue model** generating income through senpai withdrawal fees
- **Comprehensive dispute resolution** with human-mediated support and structured escalation paths
- **Advanced currency exchange system** with configurable base currency and international payment support

### Advanced Provider (Senpai) Management

- **Rigorous multi-step application process** requiring activity selection, service creation, multimedia content, and manual approval
- **Performance-based algorithmic ranking** with detailed analytics including response times, completion rates, and earnings
- **Dynamic availability management** with timezone support, day-of-week constraints, and real-time status updates
- **Multi-component reputation system** combining moderated reviews, performance metrics, and customer feedback
- **Provider status tracking** with administrative notes and availability management tools

### Celebrity & Influencer Integration

- **Enhanced visibility and featuring** with priority search placement and homepage promotion
- **Special verification badges** and priority customer support access
- **Dedicated celebrity filtering** and discovery features in platform browsing
- **Exclusive promotional tools** and enhanced profile customization options

### Advanced Communication & Chat

- **Independent multi-type chat system** supporting direct conversations with group chat infrastructure (group functionality disabled pending implementation)
- **Message archiving and audit trails** preserving deleted content for compliance and dispute resolution
- **Real-time broadcasting** with live updates, read receipts, and notification systems
- **Realtime presence tracking** for typing indicators and online status across chat and notifications
- **User blocking system** with chat-specific restrictions while maintaining other platform interactions
- **Comprehensive communication logging** for administrative oversight and dispute resolution

### Content & Media Management

- **Rich multimedia profiles** with voice samples, avatars, galleries, and automatic image processing
- **Storage bucket management** with role-based access policies and metadata tracking
- **Base64 placeholder generation** for optimized image loading and user experience

### Community & Knowledge Management

- **Comprehensive wiki system** hosting public knowledge base, development roadmaps, community rules, news, and changelog
- **Cap-based leaderboards** with opt-out privacy controls and automatic refresh scheduling
- **Gift system** allowing users to send caps and soda to providers with transfer tracking
- **Provider matching algorithm** connecting customers with suitable providers based on favorite activities
- **Profile visit tracking** monitoring user profile interactions
- **Privacy controls** for leaderboard visibility and activity display preferences
- **Community engagement features** encouraging platform participation through reward mechanisms
- **Comprehensive notification system** with deep linking and rich metadata support

### Security & Administrative Tools

- **Advanced access control** with capability-based permissions and granular role management
- **IP-based rate limiting system** with automatic cleanup and configurable throttling
- **User banning system** with admin tracking, ban reasons, and automatic role revocation
- **KYC verification system** for identity verification and compliance
- **Support ticketing system** with dispute resolution, flagging, and admin assignment
- **Comprehensive audit trails** through order logging for administrative oversight and compliance
- **Automated maintenance** via cron jobs for escrow release, data cleanup, and system optimization
- **Pre-request validation** and security measures through PostgREST configuration

### Customer Experience Features

- **Simple favoriting system** for organizing preferred senpai profiles
- **Informational availability displays** helping customers understand senpai schedules
- **Comprehensive order tracking** with real-time status updates and notification systems
- **Flexible dispute windows** with configurable timeframes and automatic processing
- **Dashboard analytics** providing withdrawal views and administrative insights

## Planned Features (Not Yet Implemented)

- **Advanced Search & Filtering**: Enhanced provider discovery with sophisticated filtering and sorting
- **Daily Tasks & Gamification**: Task management system for cap earning through daily activities
- **Achievements & Badges**: Comprehensive achievement tracking and badge system for user engagement
- **Caps Marketplace**: Spending mechanisms for caps on platform items and customizations
- **Phone Verification**: Additional identity verification beyond email authentication
