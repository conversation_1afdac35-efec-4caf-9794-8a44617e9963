# User Stories

This document contains user stories generated from the database schema analysis. These stories represent actual functionality that can be implemented based on the existing database structure.

## Table of Contents

- [Authentication & Account Management](#authentication--account-management)
- [Provider Management](#provider-management)
- [Service & Order Management](#service--order-management)
- [Communication & Chat](#communication--chat)
- [Support & Moderation](#support--moderation)
- [Content Management](#content-management)
- [Financial Transactions](#financial-transactions)

## Authentication & Account Management

1. As a user, I want to create an account with email authentication, so that I can access the platform.
2. As a user, I want to complete my profile with username, nickname, bio, and gender, so that other users can learn about me.
3. As a user, I want to set my preferred locales (en, ko, ja, tr), so that I can use the platform in my preferred language.
4. As a user, I want to upload an avatar image, so that I can personalize my profile.
5. As a user, I want to complete KYC verification with my full name, so that I can access financial features.
6. As a user, I want to add my IBAN for withdrawals, so that I can receive payments.
7. As a user, I want to set privacy preferences for activity visibility and leaderboard participation, so that I can control my public presence.
8. As a user, I want to block other users, so that I can avoid unwanted interactions.
9. As a user, I want to add social media links to my profile, so that others can find me on other platforms.
10. As a user, I want to receive notifications about important events, so that I stay informed about platform activities.
11. As a user, I want to mark notifications as read, so that I can manage my notification list.
12. As a user, I want to visit other users' profiles, so that I can learn about potential service providers.
13. As a user, I want to favorite activities, so that I can easily find services I'm interested in.
14. As an admin, I want to ban users with a reason, so that I can maintain platform safety.
15. As an admin, I want to unban users, so that I can restore access when appropriate.

## Provider Management

16. As a user, I want to apply to become a provider, so that I can offer services on the platform.
17. As a provider applicant, I want to create a provider profile with bio and slug, so that customers can find me.
18. As a provider applicant, I want to select activities I can provide, so that customers know what services I offer.
19. As a provider applicant, I want to create services with names, descriptions, and pricing, so that customers can book them.
20. As a provider applicant, I want to add service modifiers with additional costs, so that I can offer customizable services.
21. As a provider applicant, I want to upload a voice sample, so that customers can hear how I sound.
22. As a provider applicant, I want to upload at least 3 gallery images, so that customers can see my appearance.
23. As a provider applicant, I want to submit my application for review, so that I can start offering services.
24. As an admin, I want to review provider applications, so that I can approve or reject them.
25. As an approved provider, I want to set my availability schedule, so that customers know when I'm available.
26. As an approved provider, I want to toggle my status as open/closed for orders, so that I can control when I receive bookings.
27. As an approved provider, I want to fill out custom field values for my activities, so that customers have detailed information.
28. As a customer, I want to favorite providers, so that I can easily find them later.
29. As a customer, I want to ask questions to providers with cap payment, so that I can get information before booking.
30. As a provider, I want to answer customer questions and receive cap rewards, so that I can help customers and earn tokens.

## Service & Order Management

31. As a customer, I want to browse available services by activity and category, so that I can find what I need.
32. As a customer, I want to submit orders for services with escrow payment, so that my payment is protected.
33. As a provider, I want to accept or reject incoming orders, so that I can manage my workload.
34. As a provider, I want to mark orders as completed, so that I can receive payment.
35. As a customer, I want to cancel pending orders, so that I can get refunds if needed.
36. As a customer, I want to dispute completed orders within the dispute window, so that I can resolve issues.
37. As a provider, I want to refund orders when appropriate, so that I can maintain good customer relationships.
38. As a customer, I want to leave reviews and ratings for completed orders, so that I can share my experience.
39. As an admin, I want to approve reviews, so that only appropriate content is displayed.
40. As a user, I want to view provider performance metrics including ratings and completed orders, so that I can make informed decisions.
41. As a user, I want to view activity performance metrics, so that I can see which services are popular.

## Communication & Chat

42. As a customer, I want to start conversations with providers, so that I can discuss services.
43. As a user, I want to send messages in conversations, so that I can communicate with other users.
44. As a user, I want to delete my own messages, so that I can remove content I no longer want visible.
45. As an admin, I want to block conversations, so that I can prevent inappropriate communication.
46. As a user, I want to set conversation preferences, so that I can customize my chat experience.

## Support & Moderation

47. As a user, I want to create support tickets for reports or disputes, so that I can get help with issues.
48. As a user, I want to add comments to my support tickets, so that I can provide additional information.
49. As a support agent, I want to be assigned tickets, so that I can help resolve user issues.
50. As a support agent, I want to close tickets with resolution notes, so that issues are properly documented.
51. As a user, I want to flag inappropriate content or users, so that I can report violations.

## Content Management

52. As an admin, I want to create and manage wiki documents, so that I can provide platform information.
53. As an admin, I want to publish changelog entries, so that users know about platform updates.
54. As an admin, I want to create and manage platform rules, so that users understand expectations.
55. As an admin, I want to publish news articles, so that I can communicate with the community.
56. As an admin, I want to create event announcements, so that users know about upcoming activities.
57. As an admin, I want to manage the platform roadmap, so that users can see planned features.

## Financial Transactions

58. As a user, I want to deposit money to get soda and cap tokens, so that I can pay for services.
59. As a user, I want to transfer tokens to other users, so that I can send payments or gifts.
60. As a user, I want to request withdrawals of my earnings, so that I can convert tokens back to money.
61. As a user, I want to view my wallet balance and transaction history, so that I can track my finances.
62. As an admin, I want to manage supported currencies and exchange rates, so that the platform supports multiple markets.
63. As a user, I want automatic escrow handling for orders, so that payments are secure.
64. As a provider, I want to receive cap rewards for completed orders and approved reviews, so that I'm incentivized to provide good service.
65. As a customer, I want to receive cap rewards for leaving reviews and completing orders, so that I'm incentivized to participate.

## Catalog Management

66. As an admin, I want to create and manage activity categories, so that services are well-organized.
67. As an admin, I want to create activities within categories, so that providers can offer specific services.
68. As an admin, I want to create tags for activities, so that services are easily discoverable.
69. As an admin, I want to create custom fields for categories and activities, so that providers can provide detailed information.
70. As an admin, I want to create pricing models, so that providers have standardized pricing options.
71. As an admin, I want to manage platforms for social media integration, so that users can link their profiles.

## Access Control & Roles

72. As an admin, I want to assign roles to users, so that I can control platform access.
73. As an admin, I want to define capabilities for roles, so that permissions are properly managed.
74. As a user, I want automatic role assignment based on my actions (customer on signup, provider on approval), so that I have appropriate permissions.
75. As an admin, I want to revoke roles from users, so that I can manage access control.

## Media Management

76. As a user, I want to upload images with automatic placeholder generation, so that content loads quickly.
77. As a provider, I want to upload voice samples and gallery images, so that customers can evaluate my services.
78. As a user, I want to upload avatar images, so that I can personalize my profile.

## Rate Limiting & Security

79. As a platform, I want to implement rate limiting on API requests, so that the system remains stable.
80. As a platform, I want to track user visits and interactions, so that I can provide analytics and recommendations.
