# E-Senpai Platform Sitemap

This sitemap outlines the complete navigation structure for the E-Senpai platform, organized by user roles and functionality areas based on the user stories.

## Table of Contents

- [Public/Guest Pages](#publicguest-pages)
- [Authentication Pages](#authentication-pages)
- [Customer Pages](#customer-pages)
- [Provider Pages](#provider-pages)
- [Admin Pages](#admin-pages)
- [Support Agent Pages](#support-agent-pages)
- [Shared/Common Pages](#sharedcommon-pages)

---

## Public/Guest Pages

### Landing & Discovery

- `/` - Homepage with platform overview and featured providers
- `/browse` - Browse services by category and activity
- `/browse/category` - List all service categories
- `/browse/category/[slug]` - Category detail page with activities
- `/browse/activity/[slug]` - Activity detail page with available services
- `/senpai` - Browse all approved providers
- `/senpai/[username]` - Public provider profile page
- `/search` - Search services, providers, and activities
- `/leaderboard` - Top providers and performance metrics

### Information Pages

- `/about` - About E-Senpai platform
- `/how-it-works` - Platform explanation for new users
- `/pricing` - Service pricing information
- `/safety` - Safety guidelines and policies
- `/help` - Help center and FAQ
- `/contact` - Contact information

### Content Pages

- `/wiki` - Knowledge base and documentation
- `/wiki/[slug]` - Individual wiki document
- `/news` - Platform news and announcements
- `/news/[slug]` - Individual news article
- `/events` - Upcoming events and activities
- `/events/[slug]` - Event detail page
- `/changelog` - Platform updates and version history
- `/rules` - Community rules and guidelines
- `/roadmap` - Platform development roadmap

### Legal Pages

- `/terms` - Terms of service
- `/privacy` - Privacy policy
- `/cookies` - Cookie policy

---

## Authentication Pages

- `/login` - User login page
- `/signup` - User registration page
- `/verify` - Email verification page
- `/forgot-password` - Password reset request
- `/reset-password` - Password reset form
- `/logout` - Logout confirmation

---

## Customer Pages

### Dashboard & Overview

- `/dashboard` - Customer dashboard with overview
- `/inbox` - Notification center
- `/favorites` - Favorite providers and activities

### Profile & Account Management

- `/me` - Account settings hub
- `/me/profile` - Edit profile information
- `/me/avatar` - Upload/change avatar
- `/me/social-links` - Manage social media links
- `/me/privacy` - Privacy settings
- `/me/locale` - Language preferences
- `/me/kyc` - KYC verification process
- `/me/iban` - Banking information for withdrawals
- `/me/blocked-users` - Manage blocked users

### Service Booking & Orders

- `/services` - Browse available services
- `/services/[id]` - Service detail and booking page
- `/orders` - Order history and management
- `/orders/[id]` - Individual order details
- `/orders/[id]/review` - Leave review for completed order
- `/orders/[id]/dispute` - Dispute order form

### Financial Management

- `/wallet` - Wallet overview and balance
- `/wallet/deposit` - Deposit funds
- `/wallet/withdraw` - Request withdrawal
- `/wallet/transfer` - Transfer tokens to other users
- `/wallet/transactions` - Transaction history

### Communication

- `/chat` - Chat conversations list
- `/chat/[id]` - Individual conversation
- `/questions` - Asked questions to providers
- `/questions/ask/[provider-id]` - Ask question to specific provider

### Support

- `/support` - Support center
- `/support/tickets` - My support tickets
- `/support/tickets/new` - Create new support ticket
- `/support/tickets/[id]` - View support ticket details
- `/support/flag` - Report inappropriate content

---

## Provider Pages

### Provider Dashboard

- `/provider` - Provider dashboard overview
- `/provider/performance` - Performance metrics and analytics
- `/provider/earnings` - Earnings and financial overview
- `/provider/status` - Set availability and status

### Application Process

- `/provider/apply` - Start provider application
- `/provider/application` - Application form and progress
- `/provider/application/profile` - Provider profile setup
- `/provider/application/activities` - Select activities to offer
- `/provider/application/services` - Create services and pricing
- `/provider/application/media` - Upload voice and gallery images
- `/provider/application/review` - Review application before submission

### Service Management

- `/provider/services` - Manage all services
- `/provider/services/new` - Create new service
- `/provider/services/[id]` - Edit service details
- `/provider/services/[id]/modifiers` - Manage service modifiers
- `/provider/activities` - Manage provider activities
- `/provider/activities/[id]/fields` - Custom field values for activity

### Order Management

- `/provider/orders` - Incoming and active orders
- `/provider/orders/[id]` - Order details and actions
- `/provider/orders/history` - Completed order history

### Communication & Support

- `/provider/messages` - Customer conversations
- `/provider/questions` - Customer questions and answers
- `/provider/reviews` - Customer reviews and ratings

### Settings

- `/provider/profile` - Edit provider profile
- `/provider/gallery` - Manage gallery images
- `/provider/voice` - Manage voice sample
- `/provider/availability` - Set availability schedule

---

## Admin Pages

### Admin Dashboard

- `/admin` - Admin dashboard overview
- `/admin/analytics` - Platform analytics and metrics
- `/admin/reports` - System reports and insights

### User Management

- `/admin/users` - User management interface
- `/admin/users/[id]` - Individual user details
- `/admin/users/banned` - Banned users management
- `/admin/roles` - Role and permission management

### Provider Management

- `/admin/providers` - Provider overview
- `/admin/providers/applications` - Review provider applications
- `/admin/providers/applications/[id]` - Application review details
- `/admin/providers/approved` - Approved provider management
- `/admin/providers/performance` - Provider performance analytics

### Content Management

- `/admin/content` - Content management hub
- `/admin/content/wiki` - Manage wiki documents
- `/admin/content/wiki/new` - Create new wiki document
- `/admin/content/wiki/[slug]` - Edit wiki document
- `/admin/content/news` - Manage news articles
- `/admin/content/news/new` - Create news article
- `/admin/content/news/[slug]` - Edit news article
- `/admin/content/events` - Manage events
- `/admin/content/events/new` - Create event
- `/admin/content/events/[slug]` - Edit event
- `/admin/content/rules` - Manage platform rules
- `/admin/content/changelog` - Manage changelog entries
- `/admin/content/roadmap` - Manage platform roadmap

### Catalog Management

- `/admin/catalog` - Service catalog management
- `/admin/catalog/categories` - Manage categories
- `/admin/catalog/categories/new` - Create category
- `/admin/catalog/categories/[id]` - Edit category
- `/admin/catalog/activities` - Manage activities
- `/admin/catalog/activities/new` - Create activity
- `/admin/catalog/activities/[id]` - Edit activity
- `/admin/catalog/tags` - Manage tags
- `/admin/catalog/fields` - Manage custom fields
- `/admin/catalog/pricing` - Manage pricing models
- `/admin/catalog/platforms` - Manage social platforms

### Financial Management

- `/admin/finance` - Financial overview
- `/admin/finance/currencies` - Manage currencies and exchange rates
- `/admin/finance/withdrawals` - Process withdrawal requests
- `/admin/finance/transactions` - Transaction monitoring
- `/admin/finance/escrow` - Escrow management

### Review & Moderation

- `/admin/reviews` - Review approval queue
- `/admin/reviews/[id]` - Review details and approval
- `/admin/moderation` - Content moderation tools
- `/admin/moderation/flags` - Flagged content review

### System Settings

- `/admin/settings` - System configuration
- `/admin/settings/rate-limits` - Rate limiting configuration
- `/admin/settings/notifications` - Notification templates

---

## Support Agent Pages

### Support Dashboard

- `/support-agent` - Support agent dashboard
- `/support-agent/tickets` - Assigned tickets queue
- `/support-agent/tickets/[id]` - Ticket details and resolution
- `/support-agent/performance` - Support performance metrics

---

## Shared/Common Pages

### Error Pages

- `/404` - Page not found
- `/500` - Server error
- `/403` - Access forbidden
- `/maintenance` - Maintenance mode

### API Documentation (if public)

- `/api/docs` - API documentation

### Mobile App Links

- `/download` - Mobile app download links

---

## URL Patterns & Conventions

### Dynamic Routes

- `[id]` - UUID-based identifiers for orders, tickets, etc.
- `[slug]` - SEO-friendly slugs for content pages
- `[username]` - User-friendly provider usernames

### Query Parameters

- `?category=gaming` - Filter by category
- `?activity=valorant` - Filter by activity
- `?status=pending` - Filter by status
- `?page=2` - Pagination
- `?sort=rating` - Sorting options

### Internationalization

- All routes support locale prefixes: `/en/`, `/ko/`, `/ja/`, `/tr/`
- Default locale (English) has no prefix

This sitemap provides a comprehensive navigation structure that covers all user stories and ensures intuitive user journeys across different roles and use cases.
